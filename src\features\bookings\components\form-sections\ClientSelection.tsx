import { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Building2, User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAgencyClients } from '@/features/agency-clients/api/clientService';
import { ClientModal } from '@/features/agency-clients/components';
import type { AgencyClientWithContacts } from '@/features/agency-clients/types';
import { supabase } from '@/core/api/supabase';

interface ClientSelectionProps {
  selectedClientId: string | null;
  setSelectedClientId: (clientId: string | null) => void;
  userType: string;
}

const ClientSelection = ({
  selectedClientId,
  setSelectedClientId,
  userType
}: ClientSelectionProps) => {
  const { profile } = useAuth();
  const [clients, setClients] = useState<AgencyClientWithContacts[]>([]);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [showAddClientModal, setShowAddClientModal] = useState(false);
  const [agencyEntityId, setAgencyEntityId] = useState<string | null>(null);

  // Only show client selection for agencies
  if (userType !== 'agency') {
    return null;
  }

  useEffect(() => {
    fetchClients();
  }, [profile]);

  const fetchClients = async () => {
    if (!profile) return;

    try {
      setIsLoadingClients(true);

      // Get user's agency entity ID
      const { data: agencyEntities, error: entityError } = await supabase
        .from('entity_users')
        .select('entity_id')
        .eq('user_id', profile.id);

      if (entityError || !agencyEntities || agencyEntities.length === 0) {
        console.error('Error fetching agency entities:', entityError);
        return;
      }

      const entityId = agencyEntities[0].entity_id;
      setAgencyEntityId(entityId);

      // Fetch clients for this agency
      const clientsResponse = await getAgencyClients(entityId, undefined, 1, 100);
      setClients(clientsResponse.clients);
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setIsLoadingClients(false);
    }
  };

  const handleAddClientSuccess = () => {
    setShowAddClientModal(false);
    fetchClients(); // Refresh the client list
  };

  return (
    <>
      <h3 className="text-lg font-semibold">Client (Optional)</h3>
      
      <div className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="client">Select Client</Label>
          <div className="flex gap-2">
            <Select
              value={selectedClientId || ""}
              onValueChange={(value) => setSelectedClientId(value || null)}
              disabled={isLoadingClients}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder={isLoadingClients ? "Loading clients..." : "Select a client (optional)"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No client</SelectItem>
                {clients.map((client) => (
                  <SelectItem key={client.id} value={client.id}>
                    <div className="flex items-center gap-2">
                      {client.type === 'company' ? (
                        <Building2 className="h-4 w-4" />
                      ) : (
                        <User className="h-4 w-4" />
                      )}
                      <span>{client.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => setShowAddClientModal(true)}
              title="Add new client"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {selectedClientId && (
          <div className="text-sm text-muted-foreground">
            {(() => {
              const selectedClient = clients.find(c => c.id === selectedClientId);
              if (!selectedClient) return null;
              
              return (
                <div className="flex items-center gap-2 p-2 bg-muted rounded">
                  {selectedClient.type === 'company' ? (
                    <Building2 className="h-4 w-4" />
                  ) : (
                    <User className="h-4 w-4" />
                  )}
                  <span>
                    <strong>{selectedClient.name}</strong>
                    {selectedClient.email && ` • ${selectedClient.email}`}
                  </span>
                </div>
              );
            })()}
          </div>
        )}
      </div>

      {/* Add Client Modal */}
      {agencyEntityId && (
        <ClientModal
          open={showAddClientModal}
          onClose={() => setShowAddClientModal(false)}
          onSuccess={handleAddClientSuccess}
          mode="create"
        />
      )}
    </>
  );
};

export default ClientSelection;
