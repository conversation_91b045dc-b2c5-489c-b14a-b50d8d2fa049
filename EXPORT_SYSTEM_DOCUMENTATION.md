# Export System Documentation

## Overview

The export system has been cleaned up and restructured to provide a scalable, type-safe foundation for data exports. Currently supports **Bookings** and **Clients** with clear separation of concerns and future expansion capabilities.

## Architecture

### Core Components

1. **Export Configurations** (`src/core/utils/export-configs.ts`)
   - Data type interfaces
   - Column configurations
   - Export registry

2. **Export Utilities** (`src/core/utils/export-utils.ts`)
   - Core export functions
   - Format handlers (CSV, JSON)
   - Common formatters

3. **Export Hooks** (`src/core/hooks/useExport.ts`)
   - Base export hook
   - Specialized hooks for each data type

4. **Export Components** (`src/components/ui/export-button.tsx`)
   - Reusable export button with dropdown
   - Specialized booking export button
   - Simple export button variant

## Supported Export Types

### 1. Bookings Export
- **Data**: Complete booking information with artist, venue, pricing, and address details
- **Hook**: `useBookingExport`
- **Formats**: CSV, JSON
- **Usage**: Agency bookings page

### 2. Clients Export
- **Data**: Complete client information with contacts, tags, and business details
- **Hook**: `useClientExport`
- **Formats**: CSV, JSON
- **Usage**: Agency clients page

## Type Safety

```typescript
// Supported export types (extensible)
export type SupportedExportType = 'bookings' | 'clients';

// Data interfaces
export interface BookingExportData { /* ... */ }
export interface ClientExportData { /* ... */ }
```

## Usage Examples

### Booking Export
```typescript
import { useBookingExport } from '@/core';

const { isExporting, exportBookings } = useBookingExport({
  onSuccess: (count, format) => toast.success(`${count} bookings exported`),
  onError: (error) => toast.error(error.message),
});

// Export filtered bookings
await exportBookings(filteredBookings, 'csv');
```

### Client Export
```typescript
import { useClientExport } from '@/core';

const { isExporting, exportClients } = useClientExport({
  onSuccess: (count, format) => toast.success(`${count} clients exported`),
});

// Export with complete client data transformation
const exportData = clients.map(client => ({
  // ... transform client data
}));
await exportClients(exportData, 'json');
```

## Adding New Export Types

To add a new export type (e.g., 'venues'):

### 1. Define Data Interface
```typescript
// In export-configs.ts
export interface VenueExportData {
  id: string;
  name: string;
  // ... other fields
}
```

### 2. Create Column Configuration
```typescript
export const venueExportColumns: ExportColumn<VenueExportData>[] = [
  { key: 'id', label: 'Venue ID' },
  { key: 'name', label: 'Venue Name' },
  // ... other columns
];
```

### 3. Update Registry
```typescript
// Add to SupportedExportType
export type SupportedExportType = 'bookings' | 'clients' | 'venues';

// Add to getExportColumns
case 'venues':
  return venueExportColumns;

// Add to exportConfigurations
venues: {
  complete: {
    columns: venueExportColumns,
    filename: 'agency-venues',
  },
},
```

### 4. Create Specialized Hook
```typescript
export const useVenueExport = (options: UseExportOptions = {}) => {
  const exportHook = useExport(options);

  const exportVenues = useCallback(async (
    venues: any[],
    format: ExportFormat = 'csv'
  ) => {
    await exportHook.exportWithConfig(venues, 'venues', format);
  }, [exportHook]);

  return { ...exportHook, exportVenues };
};
```

## File Structure

```
src/
├── core/
│   ├── utils/
│   │   ├── export-configs.ts     # Export configurations
│   │   └── export-utils.ts       # Core export utilities
│   └── hooks/
│       └── useExport.ts          # Export hooks
├── components/
│   └── ui/
│       └── export-button.tsx     # Export UI components
└── pages/
    └── agency/
        ├── Bookings.tsx          # Uses booking export
        └── Clients.tsx           # Uses client export
```

## Benefits

1. **Type Safety**: Full TypeScript support with strict typing
2. **Scalability**: Easy to add new export types
3. **Consistency**: Standardized export patterns across the app
4. **Reusability**: Shared components and utilities
5. **Maintainability**: Clear separation of concerns
6. **User Experience**: Consistent UI with loading states and error handling

## Current Implementation Status

✅ **Bookings Export**: Fully implemented with address and description support
✅ **Clients Export**: Fully implemented with contact and tag support
✅ **Type Safety**: Complete TypeScript coverage
✅ **UI Components**: Reusable export buttons with dropdown
✅ **Error Handling**: Toast notifications and loading states
✅ **Documentation**: Clear structure for future expansion

## Removed Features

The following export types have been removed to focus on core functionality:
- Document exports
- Artist exports  
- Combined/complex exports

These can be re-added in the future following the established patterns.
